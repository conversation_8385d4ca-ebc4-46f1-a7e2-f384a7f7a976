import json
from typing import Optional
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage, SystemMessage
from langchain_openai.embeddings import OpenAIEmbeddings
from agent.config import openai_api_key
import pandas as pd
import pydantic


from agent.models import AgentState, Control, SQLWriteQuery
from agent.prompts import system_prompt, sql_prompt, sql_correction_prompt
from agent.prompts import sql_prompt, sql_correction_prompt
from agent.models import FailedSQLQuery
from agent.tools import get_default_schema_retriever_tool, execute_sql_query, get_default_schema_retriever, get_schema_retriever
from agent.utils import describe_dataframe
from agent.config import LLMs
from agent.logger import get_logger, log_agent_step, log_error
from agent.thinking_layer import get_process_tracker, ThinkingStepType

# Get logger for this module
logger = get_logger(__name__)


schema_retriever = get_schema_retriever("dw_schema_v1", OpenAIEmbeddings(openai_api_key=openai_api_key))

# Initialize model with tools lazily to avoid circular imports
def get_model_with_tools():
    schema_retriever_tool = get_default_schema_retriever_tool()
    return LLMs.base_model.bind_tools(tools=[schema_retriever_tool, SQLWriteQuery])

# define
def generate_sql_query(args: dict, max_trials = 3, recall: bool = False, recall_info: Optional[dict] = None):
    """Generates a SQL query based on the user's query"""
    
    user_query = args['user_query']
    schema_info = args['schema_info']
    if recall:
        assert recall_info is not None, "recall_info must be provided if recall is True"
        err_msg = recall_info["msg"]
        error_sql_query = recall_info["input"]
        gen_sql_prompt = sql_correction_prompt.content.format(query=user_query, schema_info=schema_info, sql_query=error_sql_query, error=err_msg)
        # print(gen_sql_prompt)
    else:
        gen_sql_prompt = sql_prompt.content.format(query=user_query, schema_info=schema_info)
    
    try:
        sql_llm = LLMs.get_sql_llm()
        res = sql_llm.invoke([AIMessage(content=gen_sql_prompt)])
        return res
    except Exception as e:
        if max_trials == 0:
            res = FailedSQLQuery(sql_query=recall_info.get("input", None), error=f"Failed to generate SQL query after 3 trials: {str(e)}")
        else:
            if isinstance(e, pydantic.ValidationError):
                err = e.errors()[0]
                res = generate_sql_query(args, max_trials - 1, True, {"msg": err['msg'], "input": err['input']})
            else:
                # {"msg": err['msg'], "input": err['input']}
                err = str(e)
                res = FailedSQLQuery(sql_query=err, error=str(e))
    return res
                

#  define a conditional edge
def router(state: AgentState):
    """Routes to the appropriate node based on the user's query"""
    msg = state["messages"][-1]
    if hasattr(msg, "tool_calls") and len(msg.tool_calls) > 0:
        return "action"
    else:
        return '__end__'

def route_from_action(state: AgentState):
    """Routes back to the agent"""
    if state["command"] == Control.AGENT:
        return 'agent'
    elif state["command"] == Control.USER:
        return "human"
    else:
        return "query_executor"
    

# Define Nodes

def initialize_system_prompt(state: AgentState):
        """Initializes the system message. The system message gives the Sherlock it's Identity and how it should behave with users"""
        if isinstance(state["messages"][0], SystemMessage):
            pass
        else:
            state["messages"].insert(0, system_prompt)

        return state

def agent(state: AgentState):
    """Calls the base model or routes to a tool or Node"""
    logger.info("🤖 Agent node activated")
    log_agent_step("agent_start", state)

    # Track thinking step
    process_id = state.get("thinking_process_id")
    if process_id:
        tracker = get_process_tracker()
        tracker.start_step(process_id, ThinkingStepType.QUERY_ANALYSIS,
                          {"node": "agent", "action": "analyzing_query"})

    initialize_system_prompt(state)
    messages = state["messages"]
    model_with_tools = get_model_with_tools()


    try:
        response = model_with_tools.invoke(messages)
        # print(messages) # .content[:100]
        logger.info(f"💭 Agent thought: {response.content[:100]}...")
        log_agent_step("agent_response", state, response_preview=response.content[:100])

        # Complete thinking step
        if process_id:
            tracker.complete_step(process_id, ThinkingStepType.QUERY_ANALYSIS,
                                output=response.content[:200],
                                details={"response_type": type(response).__name__})

        return state | {"messages": response}
    except Exception as e:
        # Fail thinking step
        if process_id:
            tracker.fail_step(process_id, ThinkingStepType.QUERY_ANALYSIS, str(e))
        logger.error(f"❌ Error in agent node: {str(e)}")
        log_error(e, "agent_node", state_summary={"message_count": len(messages)})
        raise

def ActionNode(state: AgentState):
    """Retrieves the relevant schemas and tables"""
    logger.info("🔧 Action node activated")
    log_agent_step("action_start", state)

    # Track thinking step
    process_id = state.get("thinking_process_id")
    tracker = get_process_tracker() if process_id else None

    msg = state["messages"][-1]
    outbound_msgs = []
    sql_query = ""
    command = Control.AGENT
    if hasattr(msg, "tool_calls"):
        print("tool call identified")
        for tool_call in msg.tool_calls:
            try:
                if tool_call['name'] == "schema_retreiver":
                    # Start schema retrieval step
                    if tracker:
                        tracker.start_step(process_id, ThinkingStepType.SCHEMA_RETRIEVAL,
                                         {"query": tool_call['args']['query']})

                    query = tool_call['args']['query']
                    # schema_retriever = get_schema_retriever()
                    rel_docs = schema_retriever.invoke(query)
                    schema_defs = [doc.page_content for doc in rel_docs]
                    result = '\n\n---\n'.join([json.dumps(doc.metadata, indent=2) for doc in rel_docs])

                    response = ToolMessage(content=result,
                                       tool_call_id=tool_call["id"],
                                        name=tool_call["name"])
                    state["schema_info"] = schema_defs
                    outbound_msgs.append(response)

                    # Complete schema retrieval step
                    if tracker:
                        tracker.complete_step(process_id, ThinkingStepType.SCHEMA_RETRIEVAL,
                                            output=f"Retrieved {len(rel_docs)} relevant schemas",
                                            details={"schema_count": len(rel_docs)})
                elif tool_call['name'] == "SQLWriteQuery":
                    print("SQLWriteQuery tool call identified")

                    # Start SQL generation step
                    if tracker:
                        tracker.start_step(process_id, ThinkingStepType.SQL_GENERATION,
                                         {"user_query": tool_call['args']['user_query']})

                    user_query = tool_call['args']['user_query']
                    schema_info = tool_call['args']['schema_info']
                    res = generate_sql_query({"user_query": user_query, "schema_info": schema_info})
                    # gen_sql_prompt = sql_prompt.content.format(query=user_query, schema_info=schema_info)
                    # res = sql_llm.invoke([AIMessage(content=gen_sql_prompt)])
                    # print(res)
                    if isinstance(res, FailedSQLQuery):
                        # Fail SQL generation step
                        if tracker:
                            tracker.fail_step(process_id, ThinkingStepType.SQL_GENERATION, res.error)
                        response = ToolMessage(content=res.error,
                                            tool_call_id=tool_call["id"],
                                                name=tool_call["name"])
                        outbound_msgs.append(response)
                        continue

                    # Complete SQL generation step
                    if tracker:
                        tracker.complete_step(process_id, ThinkingStepType.SQL_GENERATION,
                                            output=res.sql_query,
                                            details={"query_length": len(res.sql_query)})

                    response = ToolMessage(content=res.sql_query,
                                        tool_call_id=tool_call["id"],
                                            name=tool_call["name"])
                    sql_query = res.sql_query
                    command = Control.TOOL
                    outbound_msgs.append(response)

            except Exception as e:
                # Always return a ToolMessage on error
                response = ToolMessage(
                    content=f"Tool execution failed: {str(e)}",
                    tool_call_id=tool_call["id"],
                    name=tool_call["name"]
                )
                outbound_msgs.append(response)

                
    return state | {"messages": outbound_msgs, "sql_query": sql_query, "command": command}

def human(state: AgentState):
    """Reach out to human for feedback"""
    approval_msg = AIMessage(content="Approve agent to execute the below query")
    user_input = input("User: ")
    if user_input.lower() in {"q", "quit", "exit", "goodbye", "no", "stop", "terminate", 'never'}:
        return '__end__' 
    state["messages"].append(approval_msg)
    state["messages"].append(HumanMessage(content=user_input))
    return state | {"command": Control.AGENT}

def QueryExecutor(state: AgentState):
    """Executes the SQL query"""
    logger.info("⚡ Query executor activated")
    log_agent_step("query_executor_start", state)

    # Track thinking steps
    process_id = state.get("thinking_process_id")
    tracker = get_process_tracker() if process_id else None

    sql_query = state["sql_query"]
    logger.info(f"📊 Executing query: {sql_query[:100]}...")

    # Start query execution step
    if tracker:
        tracker.start_step(process_id, ThinkingStepType.QUERY_EXECUTION,
                          {"sql_query": sql_query[:200]})

    result = execute_sql_query(sql_query)
    command = Control.AGENT

    if isinstance(result, dict) and 'error' in result:
        # Fail query execution step
        if tracker:
            tracker.fail_step(process_id, ThinkingStepType.QUERY_EXECUTION, result['error'])
        logger.error(f"❌ Query execution failed: {result['error']}")
        log_agent_step("query_executor_error", state, error=result['error'])
        return state | {"result": None, "messages": [AIMessage(content=f"Error executing SQL query: {result['error']}")], "command": command}
    else:
        # Complete query execution and start result analysis
        if tracker:
            tracker.complete_step(process_id, ThinkingStepType.QUERY_EXECUTION,
                                output=f"Successfully executed query, returned {len(result)} rows",
                                details={"row_count": len(result)})
            tracker.start_step(process_id, ThinkingStepType.RESULT_ANALYSIS,
                             {"row_count": len(result)})

        logger.info(f"✅ Query executed successfully, processing {len(result)} rows")
        df_meta = describe_dataframe(pd.DataFrame(result))
        message = AIMessage(content=f"Here is the result of your query: {df_meta}")
        log_agent_step("query_executor_success", state, row_count=len(result))

        # Complete result analysis and start response generation
        if tracker:
            tracker.complete_step(process_id, ThinkingStepType.RESULT_ANALYSIS,
                                output=df_meta,
                                details={"data_summary": df_meta[:200]})
            tracker.start_step(process_id, ThinkingStepType.RESPONSE_GENERATION,
                             {"message_preview": message.content[:100]})

    return state | {"result": result, "messages": [message], "command": command}

            
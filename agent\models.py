from enum import Enum
from pydantic import BaseModel, Field, field_validator
from typing import Annotated, TypedDict, Optional, List, Dict, Any
from langgraph.graph.message import add_messages
from datetime import datetime
# Removed direct import of validate_sql_query to break circular dependency

class UserQuery(BaseModel):
    query: str = Field(..., description="The user's query")

class VectorSearchQuery(UserQuery):
    query_str: str = Field(..., description="The query string to search for")
    top_k: int = 3
    metadata: dict = {}

class SQLQuery(BaseModel):
    sql_query: str = Field(..., description="The SQL query to run")
    
    @field_validator("sql_query")
    @classmethod
    def validate_sql(cls, v):
        if not v.startswith("SELECT"):
            raise ValueError("SQL query must start with SELECT")
        if "FROM" not in v:
            raise ValueError("SQL query must contain FROM")

        # Lazy import to avoid circular dependency
        from agent.db import validate_sql_query
        validate = validate_sql_query(v)
        if not validate['valid']:
            if validate['type']in ('connection', 'unknown'):
                raise Exception(f"Failed to validate Query: {validate['error']}")
            raise ValueError(validate['error'])

        return v


class Control(Enum):
    AGENT = "AGENT"
    USER = "USER"
    TOOL = "TOOL"

class SQLResult(BaseModel):
    result: dict = Field(..., description="The result of the SQL query")
    sql_query: str = Field(..., description="The SQL query that was run")

class FailedSQLQuery(BaseModel):
    sql_query: str 
    error: str

class SQLWriteQuery(BaseModel):
    user_query: str = Field(..., description="The user's query")
    schema_info: str = Field(..., description="The schema information provided to you")

class AgentState(TypedDict):
    messages: Annotated[list, add_messages]
    sql_query: str
    result: dict
    query_plan: str
    schema_info: list
    command: Control = Control.AGENT
    thinking_steps: Optional[List[Dict[str, Any]]] = None
    thinking_process_id: Optional[str] = None


# Thinking Layer Models
class ThinkingStepType(Enum):
    """Types of thinking steps in the agent process"""
    QUERY_ANALYSIS = "query_analysis"
    SCHEMA_RETRIEVAL = "schema_retrieval"
    SQL_GENERATION = "sql_generation"
    QUERY_VALIDATION = "query_validation"
    QUERY_EXECUTION = "query_execution"
    RESULT_ANALYSIS = "result_analysis"
    RESPONSE_GENERATION = "response_generation"
    ERROR_HANDLING = "error_handling"


class ThinkingStepStatus(Enum):
    """Status of a thinking step"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class ThinkingStep(BaseModel):
    """Individual thinking step in the agent process"""
    step_id: str = Field(..., description="Unique identifier for the step")
    step_type: ThinkingStepType = Field(..., description="Type of thinking step")
    title: str = Field(..., description="Human-readable title for the step")
    description: str = Field(..., description="Detailed description of what this step does")
    status: ThinkingStepStatus = Field(default=ThinkingStepStatus.PENDING, description="Current status of the step")
    start_time: Optional[datetime] = Field(default=None, description="When the step started")
    end_time: Optional[datetime] = Field(default=None, description="When the step completed")
    duration_ms: Optional[int] = Field(default=None, description="Duration in milliseconds")
    details: Dict[str, Any] = Field(default_factory=dict, description="Additional step-specific details")
    error_message: Optional[str] = Field(default=None, description="Error message if step failed")
    output: Optional[str] = Field(default=None, description="Output or result of the step")


class ThinkingProcess(BaseModel):
    """Complete thinking process for an agent execution"""
    process_id: str = Field(..., description="Unique identifier for the process")
    user_query: str = Field(..., description="Original user query")
    session_id: str = Field(..., description="Session identifier")
    start_time: datetime = Field(default_factory=datetime.now, description="When the process started")
    end_time: Optional[datetime] = Field(default=None, description="When the process completed")
    status: ThinkingStepStatus = Field(default=ThinkingStepStatus.IN_PROGRESS, description="Overall process status")
    steps: List[ThinkingStep] = Field(default_factory=list, description="List of thinking steps")
    current_step_index: int = Field(default=0, description="Index of currently executing step")

    def get_current_step(self) -> Optional[ThinkingStep]:
        """Get the currently executing step"""
        if 0 <= self.current_step_index < len(self.steps):
            return self.steps[self.current_step_index]
        return None

    def get_completed_steps(self) -> List[ThinkingStep]:
        """Get all completed steps"""
        return [step for step in self.steps if step.status == ThinkingStepStatus.COMPLETED]

    def get_progress_percentage(self) -> float:
        """Get progress as percentage"""
        if not self.steps:
            return 0.0
        completed = len(self.get_completed_steps())
        return (completed / len(self.steps)) * 100
#!/usr/bin/env python3
"""
Demo script for the Thinking Layer

This script demonstrates how the thinking layer works by simulating
an agent execution with thinking steps.
"""

import time
import uuid
from agent.thinking_layer import get_process_tracker
from agent.models import ThinkingStepType


def demo_thinking_process():
    """Demonstrate the thinking layer functionality"""
    print("🧠 Sherlock Agent Thinking Layer Demo")
    print("=" * 50)
    
    # Create a thinking process
    tracker = get_process_tracker()
    process_id = tracker.create_process(
        user_query="What is the total number of farmers?",
        session_id="demo_session"
    )
    
    print(f"📝 Created thinking process: {process_id}")
    
    # Simulate agent execution with thinking steps
    steps_to_execute = [
        (ThinkingStepType.QUERY_ANALYSIS, "Analyzing user query", 1.2),
        (ThinkingStepType.SCHEMA_RETRIEVAL, "Finding relevant database schemas", 2.1),
        (ThinkingStepType.SQL_GENERATION, "Generating SQL query", 1.8),
        (ThinkingStepType.QUERY_VALIDATION, "Validating SQL query", 0.5),
        (ThinkingStepType.QUERY_EXECUTION, "Executing query against database", 3.2),
        (ThinkingStepType.RESULT_ANALYSIS, "Analyzing query results", 1.1),
        (ThinkingStepType.RESPONSE_GENERATION, "Generating user response", 0.9)
    ]
    
    for step_type, description, duration in steps_to_execute:
        print(f"\n🔄 Starting: {description}")
        
        # Start the step
        tracker.start_step(process_id, step_type, {"description": description})
        
        # Simulate processing time
        time.sleep(min(duration, 2.0))  # Cap at 2 seconds for demo
        
        # Complete the step
        output = f"Completed {description.lower()} in {duration:.1f}s"
        tracker.complete_step(process_id, step_type, output, {"duration": duration})
        
        print(f"✅ Completed: {description}")
    
    # Complete the process
    tracker.complete_process(process_id)
    
    print(f"\n🎉 Thinking process completed!")
    
    # Display process summary
    process = tracker.get_process(process_id)
    if process:
        print(f"\n📊 Process Summary:")
        print(f"   Query: {process.user_query}")
        print(f"   Total Steps: {len(process.steps)}")
        print(f"   Completed Steps: {len(process.get_completed_steps())}")
        print(f"   Progress: {process.get_progress_percentage():.1f}%")
        print(f"   Status: {process.status.value}")
        
        if process.end_time and process.start_time:
            duration = process.end_time - process.start_time
            print(f"   Total Duration: {duration.total_seconds():.1f}s")
    
    return process_id


def demo_step_details():
    """Show detailed information about thinking steps"""
    print("\n" + "=" * 50)
    print("📋 Thinking Step Details")
    print("=" * 50)
    
    tracker = get_process_tracker()
    
    # Get the last created process
    if tracker._processes:
        process_id = list(tracker._processes.keys())[-1]
        process = tracker.get_process(process_id)
        
        if process:
            for i, step in enumerate(process.steps, 1):
                print(f"\n{i}. {step.title}")
                print(f"   Type: {step.step_type.value}")
                print(f"   Status: {step.status.value}")
                print(f"   Description: {step.description}")
                
                if step.start_time:
                    print(f"   Started: {step.start_time.strftime('%H:%M:%S')}")
                
                if step.end_time:
                    print(f"   Ended: {step.end_time.strftime('%H:%M:%S')}")
                
                if step.duration_ms:
                    print(f"   Duration: {step.duration_ms}ms")
                
                if step.output:
                    print(f"   Output: {step.output}")
                
                if step.details:
                    print(f"   Details: {step.details}")


def main():
    """Main demo function"""
    try:
        # Run the thinking process demo
        process_id = demo_thinking_process()
        
        # Show step details
        demo_step_details()
        
        print(f"\n✨ Demo completed successfully!")
        print(f"💡 Process ID: {process_id}")
        print(f"🚀 You can now run the Streamlit app to see the thinking layer in action!")
        
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

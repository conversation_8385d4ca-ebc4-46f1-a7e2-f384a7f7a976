"""
UI Components for Thinking Layer

This module provides Streamlit components for displaying the agent's
thinking process in real-time.
"""

import streamlit as st
import time
from datetime import datetime
from typing import Optional, Dict, Any
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from agent.models import Thinking<PERSON>ro<PERSON>, ThinkingStep, ThinkingStepStatus, ThinkingStepType
from agent.thinking_layer import get_process_tracker


def get_step_icon(step_type: ThinkingStepType) -> str:
    """Get emoji icon for step type"""
    icons = {
        ThinkingStepType.QUERY_ANALYSIS: "🔍",
        ThinkingStepType.SCHEMA_RETRIEVAL: "📊",
        ThinkingStepType.SQL_GENERATION: "⚡",
        ThinkingStepType.QUERY_VALIDATION: "✅",
        ThinkingStepType.QUERY_EXECUTION: "🚀",
        ThinkingStepType.RESULT_ANALYSIS: "📈",
        ThinkingStepType.RESPONSE_GENERATION: "💬",
        ThinkingStepType.ERROR_HANDLING: "❌"
    }
    return icons.get(step_type, "🔧")


def get_status_color(status: ThinkingStepStatus) -> str:
    """Get color for step status"""
    colors = {
        ThinkingStepStatus.PENDING: "#6c757d",      # Gray
        ThinkingStepStatus.IN_PROGRESS: "#007bff",  # Blue
        ThinkingStepStatus.COMPLETED: "#28a745",    # Green
        ThinkingStepStatus.FAILED: "#dc3545",       # Red
        ThinkingStepStatus.SKIPPED: "#ffc107"       # Yellow
    }
    return colors.get(status, "#6c757d")


def get_status_text(status: ThinkingStepStatus) -> str:
    """Get display text for step status"""
    texts = {
        ThinkingStepStatus.PENDING: "Pending",
        ThinkingStepStatus.IN_PROGRESS: "In Progress",
        ThinkingStepStatus.COMPLETED: "Completed",
        ThinkingStepStatus.FAILED: "Failed",
        ThinkingStepStatus.SKIPPED: "Skipped"
    }
    return texts.get(status, "Unknown")


def render_thinking_step(step: ThinkingStep, is_current: bool = False) -> None:
    """Render a single thinking step"""
    icon = get_step_icon(step.step_type)
    status_color = get_status_color(step.status)
    status_text = get_status_text(step.status)
    
    # Create container with border for current step
    if is_current:
        container = st.container(border=True)
        container.markdown(f"**🎯 Current Step**")
    else:
        container = st.container()
    
    with container:
        col1, col2, col3 = st.columns([1, 6, 2])
        
        with col1:
            st.markdown(f"<div style='font-size: 24px; text-align: center;'>{icon}</div>", 
                       unsafe_allow_html=True)
        
        with col2:
            st.markdown(f"**{step.title}**")
            st.caption(step.description)
            
            # Show details if available
            if step.details:
                with st.expander("Details", expanded=False):
                    for key, value in step.details.items():
                        st.text(f"{key}: {value}")
            
            # Show output if available
            if step.output:
                with st.expander("Output", expanded=False):
                    st.code(step.output, language="text")
            
            # Show error if failed
            if step.status == ThinkingStepStatus.FAILED and step.error_message:
                st.error(f"Error: {step.error_message}")
        
        with col3:
            # Status badge
            st.markdown(f"""
                <div style='
                    background-color: {status_color}; 
                    color: white; 
                    padding: 4px 8px; 
                    border-radius: 12px; 
                    text-align: center; 
                    font-size: 12px; 
                    font-weight: bold;
                '>
                    {status_text}
                </div>
            """, unsafe_allow_html=True)
            
            # Duration if available
            if step.duration_ms:
                st.caption(f"{step.duration_ms}ms")


def render_progress_bar(process: ThinkingProcess) -> None:
    """Render overall progress bar"""
    progress = process.get_progress_percentage()
    completed_steps = len(process.get_completed_steps())
    total_steps = len(process.steps)
    
    st.progress(progress / 100, text=f"Progress: {completed_steps}/{total_steps} steps completed ({progress:.1f}%)")


def render_thinking_timeline(process: ThinkingProcess) -> None:
    """Render thinking process timeline"""
    st.subheader("🧠 Agent Thinking Process")
    
    # Progress bar
    render_progress_bar(process)
    
    # Timeline
    current_step = process.get_current_step()
    
    for i, step in enumerate(process.steps):
        is_current = current_step and step.step_id == current_step.step_id
        render_thinking_step(step, is_current)
        
        # Add separator except for last step
        if i < len(process.steps) - 1:
            st.markdown("---")


def render_thinking_summary(process: ThinkingProcess) -> None:
    """Render thinking process summary"""
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Steps", len(process.steps))
    
    with col2:
        completed = len(process.get_completed_steps())
        st.metric("Completed", completed)
    
    with col3:
        failed_steps = [s for s in process.steps if s.status == ThinkingStepStatus.FAILED]
        st.metric("Failed", len(failed_steps))
    
    with col4:
        if process.end_time and process.start_time:
            duration = process.end_time - process.start_time
            st.metric("Duration", f"{duration.total_seconds():.1f}s")
        else:
            st.metric("Duration", "In Progress")


def render_thinking_process_card(process_id: str, compact: bool = False) -> None:
    """Render thinking process in a card format"""
    tracker = get_process_tracker()
    process = tracker.get_process(process_id)
    
    if not process:
        st.warning(f"Thinking process {process_id} not found")
        return
    
    if compact:
        # Compact view for chat interface
        with st.expander(f"🧠 Thinking Process - {process.user_query[:50]}...", expanded=False):
            render_thinking_summary(process)
            render_thinking_timeline(process)
    else:
        # Full view
        st.markdown(f"### 🧠 Thinking Process")
        st.markdown(f"**Query:** {process.user_query}")
        st.markdown(f"**Started:** {process.start_time.strftime('%H:%M:%S')}")
        
        render_thinking_summary(process)
        st.markdown("---")
        render_thinking_timeline(process)


def create_thinking_process_chart(process: ThinkingProcess) -> go.Figure:
    """Create a Gantt-like chart for the thinking process"""
    fig = go.Figure()
    
    y_labels = []
    start_times = []
    durations = []
    colors = []
    
    for i, step in enumerate(process.steps):
        y_labels.append(f"{get_step_icon(step.step_type)} {step.title}")
        
        if step.start_time:
            # Calculate relative start time from process start
            start_offset = (step.start_time - process.start_time).total_seconds()
            start_times.append(start_offset)
            
            if step.end_time:
                duration = (step.end_time - step.start_time).total_seconds()
            else:
                # For in-progress steps, show current duration
                duration = (datetime.now() - step.start_time).total_seconds()
            
            durations.append(duration)
        else:
            start_times.append(0)
            durations.append(0)
        
        colors.append(get_status_color(step.status))
    
    # Create horizontal bar chart
    fig.add_trace(go.Bar(
        y=y_labels,
        x=durations,
        orientation='h',
        marker=dict(color=colors),
        text=[f"{d:.1f}s" if d > 0 else "" for d in durations],
        textposition='inside'
    ))
    
    fig.update_layout(
        title="Thinking Process Timeline",
        xaxis_title="Duration (seconds)",
        yaxis_title="Steps",
        height=400,
        showlegend=False
    )
    
    return fig


def render_live_thinking_display(process_id: str) -> None:
    """Render live thinking display that updates automatically"""
    placeholder = st.empty()
    
    while True:
        tracker = get_process_tracker()
        process = tracker.get_process(process_id)
        
        if not process:
            placeholder.warning("Process not found")
            break
        
        with placeholder.container():
            render_thinking_process_card(process_id, compact=True)
        
        # Check if process is complete
        if process.status in [ThinkingStepStatus.COMPLETED, ThinkingStepStatus.FAILED]:
            break
        
        # Wait before next update
        time.sleep(0.5)

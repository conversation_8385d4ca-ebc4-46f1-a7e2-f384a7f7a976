"""
Configuration for the Thinking Layer

This module provides configuration options for customizing the thinking layer
display and behavior.
"""

from dataclasses import dataclass
from typing import Dict, List, Optional
from agent.models import ThinkingStepType


@dataclass
class ThinkingLayerConfig:
    """Configuration for the thinking layer display"""
    
    # Display settings
    show_by_default: bool = True
    expand_by_default: bool = False
    show_step_details: bool = True
    show_step_output: bool = True
    show_step_duration: bool = True
    show_progress_bar: bool = True
    
    # Animation settings
    enable_animations: bool = True
    step_transition_delay: float = 0.5  # seconds
    
    # Step customization
    step_icons: Dict[ThinkingStepType, str] = None
    step_colors: Dict[str, str] = None
    
    # Performance settings
    max_output_length: int = 500
    max_details_items: int = 10
    
    def __post_init__(self):
        """Initialize default values"""
        if self.step_icons is None:
            self.step_icons = {
                ThinkingStepType.QUERY_ANALYSIS: "🔍",
                ThinkingStepType.SCHEMA_RETRIEVAL: "📊",
                ThinkingStepType.SQL_GENERATION: "⚡",
                ThinkingStepType.QUERY_VALIDATION: "✅",
                ThinkingStepType.QUERY_EXECUTION: "🚀",
                ThinkingStepType.RESULT_ANALYSIS: "📈",
                ThinkingStepType.RESPONSE_GENERATION: "💬",
                ThinkingStepType.ERROR_HANDLING: "❌"
            }
        
        if self.step_colors is None:
            self.step_colors = {
                "pending": "#6c757d",
                "in_progress": "#007bff",
                "completed": "#28a745",
                "failed": "#dc3545",
                "skipped": "#ffc107"
            }


# Default configuration instance
DEFAULT_CONFIG = ThinkingLayerConfig()


def get_thinking_config() -> ThinkingLayerConfig:
    """Get the current thinking layer configuration"""
    return DEFAULT_CONFIG


def update_thinking_config(**kwargs) -> None:
    """Update thinking layer configuration"""
    global DEFAULT_CONFIG
    for key, value in kwargs.items():
        if hasattr(DEFAULT_CONFIG, key):
            setattr(DEFAULT_CONFIG, key, value)


# Predefined configuration presets
PRESETS = {
    "minimal": ThinkingLayerConfig(
        show_by_default=True,
        expand_by_default=False,
        show_step_details=False,
        show_step_output=False,
        show_step_duration=False,
        enable_animations=False
    ),
    
    "detailed": ThinkingLayerConfig(
        show_by_default=True,
        expand_by_default=True,
        show_step_details=True,
        show_step_output=True,
        show_step_duration=True,
        enable_animations=True
    ),
    
    "performance": ThinkingLayerConfig(
        show_by_default=False,
        expand_by_default=False,
        show_step_details=False,
        show_step_output=False,
        show_step_duration=False,
        enable_animations=False,
        max_output_length=100,
        max_details_items=5
    )
}


def apply_preset(preset_name: str) -> bool:
    """Apply a predefined configuration preset"""
    global DEFAULT_CONFIG
    if preset_name in PRESETS:
        DEFAULT_CONFIG = PRESETS[preset_name]
        return True
    return False


def get_available_presets() -> List[str]:
    """Get list of available configuration presets"""
    return list(PRESETS.keys())

"""
Thinking Layer Implementation for Sherlock Agent

This module provides the ProcessTracker class and utilities for tracking
and displaying the agent's reasoning process in real-time.
"""

import uuid
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from threading import Lock
import streamlit as st

from agent.models import (
    ThinkingStep, 
    ThinkingProcess, 
    ThinkingStepType, 
    ThinkingStepStatus
)
from agent.logger import get_logger

logger = get_logger(__name__)


class ProcessTracker:
    """
    Tracks and manages the thinking process for agent executions.
    Provides real-time updates and state management for thinking steps.
    """
    
    def __init__(self):
        self._processes: Dict[str, ThinkingProcess] = {}
        self._lock = Lock()
        self._update_callbacks: List[Callable[[str, ThinkingProcess], None]] = []
    
    def create_process(self, user_query: str, session_id: str) -> str:
        """Create a new thinking process"""
        process_id = str(uuid.uuid4())
        
        # Define the standard thinking steps for the agent
        steps = [
            ThinkingStep(
                step_id=f"{process_id}_query_analysis",
                step_type=ThinkingStepType.QUERY_ANALYSIS,
                title="🔍 Analyzing User Query",
                description="Understanding the user's request and determining the required approach"
            ),
            ThinkingStep(
                step_id=f"{process_id}_schema_retrieval",
                step_type=ThinkingStepType.SCHEMA_RETRIEVAL,
                title="📊 Retrieving Relevant Schema",
                description="Finding relevant database tables and columns for the query"
            ),
            ThinkingStep(
                step_id=f"{process_id}_sql_generation",
                step_type=ThinkingStepType.SQL_GENERATION,
                title="⚡ Generating SQL Query",
                description="Creating the SQL query based on the schema and user request"
            ),
            ThinkingStep(
                step_id=f"{process_id}_query_validation",
                step_type=ThinkingStepType.QUERY_VALIDATION,
                title="✅ Validating Query",
                description="Checking the SQL query for correctness and safety"
            ),
            ThinkingStep(
                step_id=f"{process_id}_query_execution",
                step_type=ThinkingStepType.QUERY_EXECUTION,
                title="🚀 Executing Query",
                description="Running the SQL query against the database"
            ),
            ThinkingStep(
                step_id=f"{process_id}_result_analysis",
                step_type=ThinkingStepType.RESULT_ANALYSIS,
                title="📈 Analyzing Results",
                description="Processing and understanding the query results"
            ),
            ThinkingStep(
                step_id=f"{process_id}_response_generation",
                step_type=ThinkingStepType.RESPONSE_GENERATION,
                title="💬 Generating Response",
                description="Creating a comprehensive response for the user"
            )
        ]
        
        process = ThinkingProcess(
            process_id=process_id,
            user_query=user_query,
            session_id=session_id,
            steps=steps
        )
        
        with self._lock:
            self._processes[process_id] = process
        
        logger.info(f"🧠 Created thinking process {process_id} for query: {user_query[:50]}...")
        self._notify_callbacks(process_id, process)
        
        return process_id
    
    def start_step(self, process_id: str, step_type: ThinkingStepType, details: Optional[Dict[str, Any]] = None) -> bool:
        """Start a specific thinking step"""
        with self._lock:
            if process_id not in self._processes:
                logger.warning(f"Process {process_id} not found")
                return False
            
            process = self._processes[process_id]
            
            # Find the step to start
            step_to_start = None
            for step in process.steps:
                if step.step_type == step_type and step.status == ThinkingStepStatus.PENDING:
                    step_to_start = step
                    break
            
            if not step_to_start:
                logger.warning(f"No pending step of type {step_type} found in process {process_id}")
                return False
            
            # Update step status
            step_to_start.status = ThinkingStepStatus.IN_PROGRESS
            step_to_start.start_time = datetime.now()
            if details:
                step_to_start.details.update(details)
            
            # Update current step index
            process.current_step_index = process.steps.index(step_to_start)
            
            logger.info(f"🔄 Started step: {step_to_start.title}")
            self._notify_callbacks(process_id, process)
            
            return True
    
    def complete_step(self, process_id: str, step_type: ThinkingStepType, 
                     output: Optional[str] = None, details: Optional[Dict[str, Any]] = None) -> bool:
        """Complete a specific thinking step"""
        with self._lock:
            if process_id not in self._processes:
                return False
            
            process = self._processes[process_id]
            
            # Find the step to complete
            step_to_complete = None
            for step in process.steps:
                if step.step_type == step_type and step.status == ThinkingStepStatus.IN_PROGRESS:
                    step_to_complete = step
                    break
            
            if not step_to_complete:
                logger.warning(f"No in-progress step of type {step_type} found in process {process_id}")
                return False
            
            # Update step status
            step_to_complete.status = ThinkingStepStatus.COMPLETED
            step_to_complete.end_time = datetime.now()
            if step_to_complete.start_time:
                duration = step_to_complete.end_time - step_to_complete.start_time
                step_to_complete.duration_ms = int(duration.total_seconds() * 1000)
            
            if output:
                step_to_complete.output = output
            if details:
                step_to_complete.details.update(details)
            
            logger.info(f"✅ Completed step: {step_to_complete.title}")
            self._notify_callbacks(process_id, process)
            
            return True
    
    def fail_step(self, process_id: str, step_type: ThinkingStepType, error_message: str) -> bool:
        """Mark a thinking step as failed"""
        with self._lock:
            if process_id not in self._processes:
                return False
            
            process = self._processes[process_id]
            
            # Find the step to fail
            step_to_fail = None
            for step in process.steps:
                if step.step_type == step_type and step.status == ThinkingStepStatus.IN_PROGRESS:
                    step_to_fail = step
                    break
            
            if not step_to_fail:
                return False
            
            # Update step status
            step_to_fail.status = ThinkingStepStatus.FAILED
            step_to_fail.end_time = datetime.now()
            step_to_fail.error_message = error_message
            if step_to_fail.start_time:
                duration = step_to_fail.end_time - step_to_fail.start_time
                step_to_fail.duration_ms = int(duration.total_seconds() * 1000)
            
            # Mark process as failed
            process.status = ThinkingStepStatus.FAILED
            
            logger.error(f"❌ Failed step: {step_to_fail.title} - {error_message}")
            self._notify_callbacks(process_id, process)
            
            return True
    
    def complete_process(self, process_id: str) -> bool:
        """Mark the entire thinking process as completed"""
        with self._lock:
            if process_id not in self._processes:
                return False
            
            process = self._processes[process_id]
            process.status = ThinkingStepStatus.COMPLETED
            process.end_time = datetime.now()
            
            logger.info(f"🎉 Completed thinking process {process_id}")
            self._notify_callbacks(process_id, process)
            
            return True
    
    def get_process(self, process_id: str) -> Optional[ThinkingProcess]:
        """Get a thinking process by ID"""
        with self._lock:
            return self._processes.get(process_id)
    
    def add_update_callback(self, callback: Callable[[str, ThinkingProcess], None]):
        """Add a callback to be notified of process updates"""
        self._update_callbacks.append(callback)
    
    def _notify_callbacks(self, process_id: str, process: ThinkingProcess):
        """Notify all registered callbacks of process updates"""
        for callback in self._update_callbacks:
            try:
                callback(process_id, process)
            except Exception as e:
                logger.error(f"Error in thinking layer callback: {e}")


# Global process tracker instance
_process_tracker = ProcessTracker()


def get_process_tracker() -> ProcessTracker:
    """Get the global process tracker instance"""
    return _process_tracker


def track_thinking_step(process_id: str, step_type: ThinkingStepType, 
                       details: Optional[Dict[str, Any]] = None):
    """Decorator to track thinking steps"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            tracker = get_process_tracker()
            
            # Start the step
            tracker.start_step(process_id, step_type, details)
            
            try:
                # Execute the function
                result = func(*args, **kwargs)
                
                # Complete the step
                output = str(result) if result else None
                tracker.complete_step(process_id, step_type, output)
                
                return result
            except Exception as e:
                # Fail the step
                tracker.fail_step(process_id, step_type, str(e))
                raise
        
        return wrapper
    return decorator

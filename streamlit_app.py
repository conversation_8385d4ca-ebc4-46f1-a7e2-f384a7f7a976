#!/usr/bin/env python3
"""
Streamlit UI for Sherlock Agent - AFEX's AI Data Analyst
A user-friendly interface for interacting with the Sherlock Agent system.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import json
import uuid
from pathlib import Path
import time
from agent.plot_utils import auto_plot_from_dataframe
from agent.ui_components import render_thinking_process_card, render_live_thinking_display
from agent.thinking_layer import get_process_tracker

# Configure Streamlit page
st.set_page_config(
    page_title="Sherlock Agent - AFEX AI Data Analyst",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }

    .sql-code {
        background-color: #f5f5f5;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #4caf50;
        font-family: 'Courier New', monospace;
    }
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
    }

    .thinking-step {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1rem;
        margin: 0.5rem 0;
        transition: all 0.3s ease;
    }

    .thinking-step:hover {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .thinking-step.current {
        border-left: 4px solid #007bff;
        background-color: #e7f3ff;
    }

    .thinking-step.completed {
        border-left: 4px solid #28a745;
    }

    .thinking-step.failed {
        border-left: 4px solid #dc3545;
        background-color: #ffeaea;
    }

    .progress-container {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
def initialize_session_state():
    """Initialize Streamlit session state variables"""
    if 'sessions' not in st.session_state:
        st.session_state.sessions = {}
    if 'current_session_id' not in st.session_state:
        st.session_state.current_session_id = None
    if 'agent' not in st.session_state:
        st.session_state.agent = None
    if 'agent_initialized' not in st.session_state:
        st.session_state.agent_initialized = False

def create_new_session():
    """Create a new chat session"""
    session_id = str(uuid.uuid4())[:8]
    session_name = f"Session {len(st.session_state.sessions) + 1}"
    
    st.session_state.sessions[session_id] = {
        'name': session_name,
        'created_at': datetime.now(),
        'messages': [],
        'queries_executed': 0,
        'total_rows_returned': 0
    }
    st.session_state.current_session_id = session_id
    return session_id

def initialize_agent():
    """Initialize the Sherlock Agent"""
    if not st.session_state.agent_initialized:
        try:
            with st.spinner("🚀 Initializing Sherlock Agent..."):
                from agent.workflow import Agent
                from agent.logger import get_logger
                
                logger = get_logger("streamlit_app")
                logger.info("Initializing Sherlock Agent from Streamlit UI")
                
                st.session_state.agent = Agent()
                st.session_state.agent.build_workflow()
                st.session_state.agent_initialized = True
                
                logger.info("Sherlock Agent successfully initialized in Streamlit UI")
                return True
        except Exception as e:
            st.error(f"❌ Failed to initialize agent: {str(e)}")
            return False
    return True

def sidebar():
    """Create the sidebar with session management"""
    st.sidebar.markdown("## 🔍 Sherlock Agent")
    st.sidebar.markdown("*AFEX's AI Data Analyst*")
    st.sidebar.divider()
    
    # Session Management
    st.sidebar.markdown("### 📝 Sessions")
    
    # Create new session button
    if st.sidebar.button("➕ New Session", use_container_width=True):
        create_new_session()
        st.rerun()
    
    # Session selector
    if st.session_state.sessions:
        session_options = {
            session_id: f"{data['name']} ({data['created_at'].strftime('%H:%M')})"
            for session_id, data in st.session_state.sessions.items()
        }
        
        selected_session = st.sidebar.selectbox(
            "Select Session:",
            options=list(session_options.keys()),
            format_func=lambda x: session_options[x],
            index=list(session_options.keys()).index(st.session_state.current_session_id) 
                  if st.session_state.current_session_id in session_options else 0
        )
        
        if selected_session != st.session_state.current_session_id:
            st.session_state.current_session_id = selected_session
            st.rerun()
        
        # Session stats and controls
        if st.session_state.current_session_id:
            current_session = st.session_state.sessions[st.session_state.current_session_id]
            st.sidebar.markdown("#### 📊 Session Stats")
            st.sidebar.metric("Messages", len(current_session['messages']))
            st.sidebar.metric("Queries Executed", current_session['queries_executed'])
            st.sidebar.metric("Total Rows", current_session['total_rows_returned'])

            # Clear session button in sidebar
            st.sidebar.markdown("#### 🔧 Session Controls")
            if st.sidebar.button("🗑️ Clear Session", use_container_width=True):
                current_session['messages'] = []
                current_session['queries_executed'] = 0
                current_session['total_rows_returned'] = 0
                st.rerun()
    
    st.sidebar.divider()
    
    # Agent Status
    st.sidebar.markdown("### 🤖 Agent Status")
    if st.session_state.agent_initialized:
        st.sidebar.success("✅ Agent Ready")
    else:
        st.sidebar.warning("⚠️ Agent Not Initialized")
        if st.sidebar.button("🔄 Initialize Agent"):
            initialize_agent()
            st.rerun()
    
    # Thinking Layer Controls
    st.sidebar.divider()
    st.sidebar.markdown("### 🧠 Thinking Layer")

    # Initialize thinking layer settings if not exists
    if 'show_thinking_layer' not in st.session_state:
        st.session_state.show_thinking_layer = True
    if 'thinking_layer_expanded' not in st.session_state:
        st.session_state.thinking_layer_expanded = False

    st.session_state.show_thinking_layer = st.sidebar.checkbox(
        "Show Agent Thinking Process",
        value=st.session_state.show_thinking_layer,
        help="Display the agent's reasoning steps in real-time"
    )

    if st.session_state.show_thinking_layer:
        st.session_state.thinking_layer_expanded = st.sidebar.checkbox(
            "Expand Thinking Details",
            value=st.session_state.thinking_layer_expanded,
            help="Show detailed information for each thinking step"
        )

    # System Info
    st.sidebar.divider()
    st.sidebar.markdown("### ℹ️ System Info")
    st.sidebar.info("""
    **Sherlock Agent** helps you analyze AFEX's data by:
    - Understanding business questions
    - Writing optimized SQL queries
    - Executing queries safely
    - Providing actionable insights
    """)

def display_chat_message(message_type, content, timestamp=None):
    """Display a chat message using Streamlit's native chat components"""
    if timestamp is None:
        timestamp = datetime.now()

    # Use Streamlit's native chat message component
    with st.chat_message(message_type, avatar="👤" if message_type == "user" else "🔍"):
        st.markdown(f"**{timestamp.strftime('%H:%M:%S')}**")
        st.markdown(content)

def display_sql_query(sql_query):
    """Display SQL query with syntax highlighting"""
    st.markdown("#### 📝 Generated SQL Query")
    st.markdown(f"""
    <div class="sql-code">
        <code>{sql_query}</code>
    </div>
    """, unsafe_allow_html=True)

def display_data_results(data):
    """Display query results with visualizations"""
    if not data or (isinstance(data, dict) and 'error' in data):
        st.error(f"❌ Query failed: {data.get('error', 'Unknown error')}")
        return 0
    
    df = pd.DataFrame.from_dict(data)
    row_count = len(df)
    
    st.markdown("#### 📊 Query Results")
    
    # Display metrics
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Rows Returned", row_count)
    with col2:
        st.metric("Columns", len(df.columns))
    with col3:
        execution_time = "< 1s"  # Placeholder - could be tracked from agent
        st.metric("Execution Time", execution_time)
    
    # Display data table
    st.dataframe(df, use_container_width=True)
    
    # Auto-generate simple visualizations for numeric data
    # numeric_columns = df.select_dtypes(include=['number']).columns
    # if len(numeric_columns) > 0 and row_count > 1:
    #     st.markdown("#### 📈 Quick Visualization")
        
    #     if len(numeric_columns) == 1:
    #         # Single numeric column - histogram
    #         fig = px.histogram(df, x=numeric_columns[0], title=f"Distribution of {numeric_columns[0]}")
    #         st.plotly_chart(fig, use_container_width=True)
    #     elif len(numeric_columns) >= 2:
    #         # Multiple numeric columns - scatter plot
    #         fig = px.scatter(df, x=numeric_columns[0], y=numeric_columns[1], 
    #                        title=f"{numeric_columns[0]} vs {numeric_columns[1]}")
    try:
        plot_vars = auto_plot_from_dataframe(df)
        if 'fig' in plot_vars:
            fig = plot_vars['fig']
            st.pyplot(fig, use_container_width=True)
    except Exception as e:
        st.error(f"❌ Error generating plot: {str(e)}")

    return row_count

def main_chat_interface():
    """Main chat interface"""
    st.markdown('<h1 class="main-header">🔍 Sherlock Agent - AFEX AI Data Analyst</h1>', 
                unsafe_allow_html=True)
    
    # Check if agent is initialized
    if not st.session_state.agent_initialized:
        st.warning("⚠️ Agent not initialized. Please initialize the agent from the sidebar.")
        return
    
    # Check if session exists
    if not st.session_state.current_session_id:
        st.info("👋 Welcome! Create a new session from the sidebar to start chatting with Sherlock.")
        st.markdown("""
        ### 💡 Sample Questions to Try:
        - "What is the total number of farmers?"
        - "Show me the top 10 commodities by volume"
        - "What are the average loan amounts by region?"
        - "Display client segments and their counts"
        """)
        return
    
    current_session = st.session_state.sessions[st.session_state.current_session_id]
    
    # Display chat history
    if current_session['messages']:
        st.markdown("### 💬 Conversation")

        # Chat container with better organization
        chat_container = st.container()
        with chat_container:
            for message in current_session['messages']:
                display_chat_message(
                    message['type'],
                    message['content'],
                    message['timestamp']
                )

                # Display SQL and results if available (only for agent messages)
                if message['type'] == 'agent':
                    # Display thinking process if available and enabled
                    if (st.session_state.show_thinking_layer and
                        message.get('thinking_process_id')):
                        render_thinking_process_card(
                            message['thinking_process_id'],
                            compact=True
                        )

                    if message.get('sql_query'):
                        display_sql_query(message['sql_query'])

                    if message.get('data'):
                        display_data_results(message['data'])
    else:
        st.markdown("### 💬 Start a Conversation")
        st.info("Ask Sherlock a question using the chat input below!")
    
    # Chat input using streamlit's chat_input
    user_input = st.chat_input(
        placeholder="Ask Sherlock about AFEX's data... e.g., What is the total number of farmers?"
    )

    # Process user input
    if user_input and user_input.strip():
        # Add user message
        user_message = {
            'type': 'user',
            'content': user_input,
            'timestamp': datetime.now()
        }
        current_session['messages'].append(user_message)
        
        # Create placeholder for thinking layer
        thinking_placeholder = None
        if st.session_state.show_thinking_layer:
            thinking_placeholder = st.empty()

        # Get agent response
        with st.spinner("🤔 Sherlock is thinking..."):
            try:
                response = st.session_state.agent.chat(user_input, st.session_state.current_session_id)

                # Display thinking process if enabled
                if st.session_state.show_thinking_layer and response.get('thinking_process_id'):
                    with thinking_placeholder.container():
                        render_thinking_process_card(
                            response['thinking_process_id'],
                            compact=not st.session_state.thinking_layer_expanded
                        )

                # Add agent response
                agent_message = {
                    'type': 'agent',
                    'content': response['response'],
                    'timestamp': datetime.now(),
                    'sql_query': response.get('sql_query'),
                    'data': response.get('data'),
                    'thinking_process_id': response.get('thinking_process_id')
                }
                current_session['messages'].append(agent_message)

                # Update session stats
                current_session['queries_executed'] += 1
                if response.get('data') and isinstance(response['data'], list):
                    current_session['total_rows_returned'] += len(response['data'])

            except Exception as e:
                st.error(f"❌ Error: {str(e)}")
                agent_message = {
                    'type': 'agent',
                    'content': f"I apologize, but I encountered an error: {str(e)}",
                    'timestamp': datetime.now()
                }
                current_session['messages'].append(agent_message)
        
        st.rerun()

def main():
    """Main application function"""
    initialize_session_state()
    
    # Initialize agent on startup
    if not st.session_state.agent_initialized:
        initialize_agent()
    
    # Create sidebar
    sidebar()
    
    # Main interface
    main_chat_interface()

if __name__ == "__main__":
    main()
